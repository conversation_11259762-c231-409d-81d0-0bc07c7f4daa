import json
import os
import sys
import time
import random
from tqdm import tqdm

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if ROOT_DIR not in sys.path: sys.path.append(ROOT_DIR)
print(ROOT_DIR)

from pathlib import Path
from dotenv import load_dotenv
from typing import Optional, Union

class EnvConfig:
    """环境变量配置管理器"""
    _instance = None
    
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(EnvConfig, cls).__new__(cls)
        return cls._instance
    
    def __init__(self, env_path: Union[str, Path] = None):
        self.sc_keys = None # 硅基流动
        self.sc_keys_balance = None

        # 记录加载 key 的时间，用于判断是否需要重新加载
        self.load_keys_time = time.time()
        self.interval_time = 60 * 60  # 60分钟
        
        # 优先级：手动指定 > 项目根目录 .env > 系统环境变量
        self.env_path = env_path or Path(__file__).parent.parent.parent / ".env.local"
        print(self.env_path)

        self.load_environment()
        self.load_keys()
    
    def may_load_keys(func):
        """装饰器：在调用方法前自动加载或刷新API密钥"""
        def wrapper(self, *args, **kwargs):
            self.load_keys()
            return func(self, *args, **kwargs)
        return wrapper

    def load_keys(self):
        def load_keys_from_file():
            """从文件中加载 key"""
            RROOT_DIR = ROOT_DIR
            with open(os.path.join(RROOT_DIR, "siliconflow.key"), "r") as f:
                self.sc_keys = [line.strip() for line in f if line.strip()]
            with open(os.path.join(RROOT_DIR, "siliconflow_balance.key"), "r") as f:
                self.sc_keys_balance = [line.strip() for line in f if line.strip()]
            self.load_keys_time = time.time()

        if self.sc_keys is None or self.sc_keys_balance is None:
            # 如果没有加载过 key，则加载
            print("初始化 keys")
            load_keys_from_file()
        elif time.time() - self.load_keys_time > self.interval_time:
            # 如果超过了时间间隔，则重新加载
            print("超时，重新加载 keys")
            load_keys_from_file()

    def load_environment(self):
        """加载环境变量"""
        if isinstance(self.env_path, Path) and self.env_path.exists():
            load_dotenv(dotenv_path=self.env_path, override=True)
        elif isinstance(self.env_path, str) and Path(self.env_path).exists():
            load_dotenv(dotenv_path=self.env_path, override=True)

    def get(self, key: str, default: Optional[str] = None, required: bool = False) -> str:
        """获取环境变量（带验证功能）"""
        value = os.getenv(key, default)
        
        if required and not value:
            raise EnvironmentError(f"Required environment variable {key} is missing")
            
        return value or ""  # 保证返回字符串类型

    @may_load_keys
    def get_sc_key(self, index: int = -1, have_balance: bool = False) -> str:
        """
        获取硅基流动随机模型key
        如果 index 为 -1，则随机选择一个key
        否则，选择第 index 个key
        如果 have_balance 为 True，则优先选择余额大于0.1的key
        """
        # 获取对应的 keys
        cur_keys = self.sc_keys_balance if have_balance else self.sc_keys

        # 随机选择一个key
        if index == -1:
            return random.choice(cur_keys).strip()

        # 按照 index 选择 key
        if index >= len(cur_keys):
            print(f"警告：索引 {index} 超出了可用密钥范围，将从头循环选择")
            index = index % len(cur_keys)
        return cur_keys[index].strip()

# 初始化配置（项目启动时调用）
config = EnvConfig()

def test_sc_balance():
    """测试硅基流动的财务情况"""

    import requests
    url = "https://api.siliconflow.cn/v1/user/info"

    # 加载文件 ROOT_DIR + siliconflow.key
    RROOT_DIR = ROOT_DIR
    with open(os.path.join(RROOT_DIR, "siliconflow.key"), "r") as f:
        keys = f.readlines()
    
    infos = []
    for key in tqdm(keys):
        headers = {"Authorization": f"Bearer {key.strip()}"}
        response = requests.request("GET", url, headers=headers)
        info = json.loads(response.text)
        infos.append(info)
        
        if float(info['data']['balance']) < 0.1:
            print(f"余额不足0.1的key: {key.strip()}")

    # 保存到文件
    with open(os.path.join(RROOT_DIR, "siliconflow_balance.json"), "w") as f:
        f.write(json.dumps(infos, indent=4))

    return infos

# --------------------------
#           使用示例
# --------------------------

if __name__ == "__main__":
    # 获取带默认值的配置
    # db_host = config.get("DB_HOST", "localhost")
    
    # # 获取必须存在的配置（不存在会抛异常）
    # try:
    #     api_key = config.get("API_KEY", required=True)
    # except EnvironmentError as e:
    #     print(f"Configuration error: {e}")
    #     exit(1)


    out = config.get_sc_key()
    # out = config.get("API_KEY_AL")
    print(out)
    # test_sc_balance()
