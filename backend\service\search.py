import os
import sys

# 获取项目根目录的绝对路径
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if ROOT_DIR not in sys.path: sys.path.append(ROOT_DIR)

import re
import time
import asyncio
from utils.llm import BaseLLM, ModelSelector, BaseEmbedding, BaseZilliz
from utils.config import config

# 连接 Milvus
client_hybrid_bom = BaseZilliz(**ModelSelector.hybrid_bom)

# 初始化 Embedding 模型
embeddings_qwen4b = BaseEmbedding(**ModelSelector.sc_qwen3_embedding_4b)

class ScoreTools:
    @staticmethod
    def get_matching_prompt():
        query_matching_score_cn = '''
            你是一名 BOM 匹配专家，需要根据用户提供的查询需求 <Query> 和 待匹配零件信息 <Results>，严格评估两者的匹配度。请按以下规则输出评分：

            **评分规则**
            1. 10分制（整数），6分为及格线，主要衡量零件是否符合用户需求，若不在评分维度内但与用户需求相关，可酌情打6分以上

            2. 评分维度：
            - (1) 型号匹配度（型号是否完全或部分匹配）重要性：100%
            - (2) 品牌匹配度（品牌是否匹配）重要性：80%
            - (3) 参数匹配度（技术参数是否匹配）重要性：90%
            - (4) 封装匹配度（封装形式是否匹配）重要性：70%
            - (5) 功能匹配度（器件功能描述是否匹配）重要性：60%

            3. 评分标准（6分以上请严格核查，不要随意打高分）
            - 9-10分：完美匹配评分维度(1)(2)(3)
            - 7-8分：匹配评分维度(1)，且部分匹配评分维度(2)(3)
            - 6分：部分匹配评分维度(1)或(3)
            - 3-5分：部分匹配评分维度(4)(5)
            - 1-2分：在评分维度上基本无关
            - 0分：无任何关联

            **输出要求**
            1. 仅返回Python列表格式的分数：[x, x, x, ...]
            2. 严格保持与results相同的顺序
            3. 不包含任何解释性文字
        '''
        return query_matching_score_cn

    @staticmethod
    def get_strict_matching_prompt():
        system_prompt = '''
            你是一名 BOM 匹配专家，需要根据用户提供的查询需求 和 待匹配零件信息，严格评估两者的匹配度。
        '''
        user_prompt = '''

        # 查询需求为：
        {Query}

        # 待匹配零件信息为：
        {Results}

        # 请按以下规则输出评分：
            **评分规则**
            1. 10分制（整数），6分为及格线，主要衡量零件是否符合用户需求，若不在评分维度内但与用户需求相关，可酌情打6分以上

            2. 评分维度：
            - (1) 型号匹配度（型号是否完全或部分匹配）重要性：100%
            - (2) 品牌匹配度（品牌是否匹配）重要性：80%
            - (3) 参数匹配度（技术参数是否匹配）重要性：90%
            - (4) 封装匹配度（封装形式是否匹配）重要性：70%
            - (5) 功能匹配度（器件功能描述是否匹配）重要性：60%

            3. 评分标准（6分以上请严格核查，不要随意打高分）
            - 9-10分：完美匹配评分维度(1)(2)(3)
            - 7-8分：匹配评分维度(1)，且部分匹配评分维度(2)(3)
            - 6分：部分匹配评分维度(1)或(3)
            - 3-5分：部分匹配评分维度(4)(5)
            - 1-2分：在评分维度上基本无关
            - 0分：无任何关联

            **输出要求**
            1. 注意由于用户经常会不给出自己所给参数所对应的 列名，因此匹配的时候你需要自己查看用户搜索内容对应的列
            1. 仅返回列表格式的分数：[x, x, x, ...]
            2. 严格保持与results相同的顺序，并且和 results 数量一致，即一共有 {Number} 个分数
            3. 不包含任何解释性文字，首个字符和最终字符为中括号
        '''
        return system_prompt, user_prompt

    @staticmethod
    async def get_scores(model: BaseLLM, query: str, results: list[str], default_score=6, match_limit=4):
        '''
            单个模型 对一个 query - results 对的评分，只需 ask 单次
        Args:
            model: 模型名称
            query: 查询字符串
            results: 该 query 的搜索结果列表
            default_score: 正则匹配出错时的默认分数
            limit: 单词匹配的 results 数量（防止过多导致上下文过长）
            match_limit: 每次 ask 的 results 数量（防止过多导致上下文过长）
        Returns:
            评分列表
        '''

        # 1. 定义子函数，用于对每个切分后的 results 进行评分
        async def sub_scores(query: str, matchs: list[str]):
            try:
                model_curr = model.__copy__()
                model_curr.api_key = config.get_sc_key(have_balance=not model.is_free)

                # 1. 构建 prompt
                matchs_str = [f"{i+1}. {out}\n" for i, out in enumerate(matchs)]
                matchs_str = ''.join(matchs_str)
                prompt = f"Query: {query} \n Results: {matchs_str} \n\n Please output scores list:"

                # 2. 评分，添加超时控制
                try:
                    result = await asyncio.wait_for(
                        model_curr.ask_async(
                            prompt, 
                            system_prompt=ScoreTools.get_matching_prompt(), 
                            temperature=0,  # 降低温度以获得更稳定的结果
                            max_tokens=15,    # 限制输出长度
                            top_k=1,
                        ),
                        timeout=3.5
                    )
                except asyncio.TimeoutError:
                    print(f"timeout: {model_curr.name} 响应超时，打默认分数")
                    return [default_score] * len(matchs)

                # 3. 提取分数
                result = re.search(r'\[([\d,\s]+)\]', result)
                if not result:
                    # 提取错误 -> 打默认分数
                    print(f"re failed: {model_curr.name} 正则匹配 scores 失败，打默认分数")
                    return [default_score] * len(matchs)
                else:
                    # 确保分数顺序与原始结果顺序一致
                    numbers = [int(x.strip()) for x in result.group(1).split(',')]
                    if len(numbers) != len(matchs):
                        # 分数数量与结果数量不一致 -> 打默认分数
                        print(f"number failed: {model_curr.name} scores 数量与 results 数量不一致，打默认分数")
                        return [default_score] * len(matchs)
                    return numbers
                
            except Exception as e:
                # 其他错误 -> 打默认分数
                print(f"other failed: {model_curr.name} 打分出错，打默认分数")
                print(f"error: {e}")
                return [default_score] * len(matchs)

        # 2. 按照 limit 分页，并逐个分发进行评分
        tasks = [sub_scores(query, results[i:i+match_limit]) for i in range(0, len(results), match_limit)]
        scores_list = await asyncio.gather(*tasks)
        scores = [score for sub_scores in scores_list for score in sub_scores]

        return scores

    @staticmethod
    async def get_strict_scores(model: BaseLLM, query: str, results: list[str], default_score=6, match_limit=4) -> list[int]:
        '''
            单个模型 对一个 query - results 对的严格格式评分，防止出现格式错误的情况
        Args:
            model: 模型实例
            query: 查询字符串
            results: 该 query 的搜索结果列表
            default_score: 正则匹配出错时的默认分数
            match_limit: 每次 ask 的 results 数量（防止过多导致上下文过长）
        Returns:
            评分列表
        '''

        # 1. 定义子函数，用于对每个切分后的 results 进行严格评分
        async def sub_scores(query: str, matchs: list[str]):
            try:
                model_curr = model.__copy__()
                model_curr.api_key = config.get_sc_key(have_balance=not model.is_free)

                # 1. 构建 prompt
                matchs_str = [f"{i+1}. {out}\n" for i, out in enumerate(matchs)]
                matchs_str = ''.join(matchs_str)
                
                # 2. 获取严格评分的 prompt
                system_prompt, user_prompt = ScoreTools.get_strict_matching_prompt()
                formatted_prompt = user_prompt.format(Query=query, Results=matchs_str, Number=len(matchs))

                # 3. 评分，添加超时控制
                try:
                    result = await asyncio.wait_for(
                        model_curr.ask_async(
                            formatted_prompt, 
                            system_prompt=system_prompt, 
                            temperature=0,  # 降低温度以获得更稳定的结果
                            max_tokens=25,    # 限制输出长度
                            top_k=1,
                        ),
                        timeout=4.5
                    )
                except asyncio.TimeoutError:
                    print(f"timeout: {model_curr.name} 严格评分响应超时，打默认分数")
                    return [default_score] * len(matchs)

                # 3. 提取分数
                old_result = result
                result = re.search(r'\[([\d,\s]+)\]', result)
                if not result:
                    # 提取错误 -> 打默认分数
                    print(f"re failed: {model_curr.name} 正则匹配 scores 失败 {old_result}，打默认分数")
                    return [default_score] * len(matchs)
                else:
                    # 确保分数顺序与原始结果顺序一致
                    numbers = [int(x.strip()) for x in result.group(1).split(',')]
                    if len(numbers) != len(matchs):
                        # 分数数量与结果数量不一致 -> 打默认分数
                        print(f"number failed: {model_curr.name} scores 数量与 results 数量不一致，打默认分数")
                        return [default_score] * len(matchs)
                    return numbers
                
            except Exception as e:
                # 其他错误 -> 打默认分数
                print(f"other failed: {model_curr.name} 严格评分出错，打默认分数")
                print(f"error: {e}")
                return [default_score] * len(matchs)

        # 2. 按照 limit 分页，并逐个分发进行评分
        tasks = [sub_scores(query, results[i:i+match_limit]) for i in range(0, len(results), match_limit)]
        scores_list = await asyncio.gather(*tasks)
        scores = [score for sub_scores in scores_list for score in sub_scores]

        return scores

    @staticmethod
    async def get_scores_by_models(models: list[BaseLLM], query: str, results: list[str], default_score=6, match_limit=4) -> list[int]:
        ''' 
            多个模型 对一个 query - results 对的评分，并发 ask 多次
        Args:
            models: 模型列表
            query: 搜索关键词
            results: 搜索结果
            default_score: 正则匹配出错时的默认分数
            match_limit: 每次 ask 的 results 数量（防止过多导致上下文过长）
        Returns:
            评分列表
        '''

        # # 1. 多模型并行获取 scores
        # for model in models:
        #     model.api_key = config.get_sc_key()

        # 1. 多模型并行获取 scores
        tasks = [ScoreTools.get_strict_scores(model, query, results, default_score, match_limit) for model in models]
        # tasks = [ScoreTools.get_scores(model, query, results, default_score, match_limit) for model in models]
        scores_list = await asyncio.gather(*tasks)

        # 2. 计算平均分
        scores = [sum(score) / len(score) for score in zip(*scores_list)]

        return scores

async def semantic_search_hybrid(query: str, limit: int = 12, folder_filter: str = '',
                                 output_fields: list[str] = ["primary_key", "data"]):
    """根据搜索内容返回语义搜索结果。
    Args:
        query (str): 用户输入的搜索关键词或语句。
        limit (int): 返回结果数量限制。
        folder_filter (str): 文件夹过滤条件。
        output_fields (list[str]): 输出字段列表。
    """

    query_all = query

    # 2. 构建过滤条件
    if folder_filter:
        filter = f'primary_key like "{folder_filter}%"'
    else:
        filter = ''
    print(f"filter: {filter}")

    # 3. 获取 embedding，并进行超时检测
    start_time = time.time()
    vector = await embeddings_qwen4b.aembedding([query_all], vector_length=2048, timeout=10)
    embed_time = time.time() - start_time

    # 4. 进行语义匹配
    start_time = time.time()
    client = client_hybrid_bom
    results = client.hybrid_search(data=vector[0],
                            query=query_all,
                            output_fields=output_fields,
                            limit=limit,
                            expr=filter if filter else None,
                            )
    search_time = time.time() - start_time

    # 5. 添加日志
    print(f"step1 总耗时: {embed_time + search_time:.2f}s || 向量化：{embed_time:.2f}s, 搜索：{search_time:.2f}s")

    # 6. 结构化搜索结果
    out_contents = []
    for hit in results:
        # 直接访问 entity 字典中的字段
        entity = hit.get('entity', {})
        content = {}
        for field in output_fields:
            content[field] = str(entity.get(field, ''))
        # 添加相似度分数
        content['distance'] = float(hit.get('distance', 0.0))
        out_contents.append(content)

    return out_contents

async def deep_semantic_search_hybrid(query: str,
                               limit: int = 12,
                               folder_filter: str = '',
                               match_limit: int = 2,
                               output_fields: list[str] = ["primary_key", "data"]):
    """根据搜索内容，返回语义搜索结果（使用 llm 打分以进行深度搜索）
    Args:
        query (str): 用户输入的搜索关键词或语句。
        limit (int, optional): 每页返回的结果数量。默认为12。
        folder_filter (str, optional): 文件夹过滤条件。默认为空字符串。
        match_limit (int, optional): 每次 query 匹配的 result 数量。越小效果越好，但会增加请求次数，提高 token 用量。默认为2。
        output_fields (list[str], optional): 输出字段列表。默认为["primary_key", "data"]。
    Returns:
        list[dict]: 搜索结果列表，每个结果包含字段和相似度分数。示例：
            [
                {
                    "primary_key": "folder_0",
                    "data": "零件信息JSON字符串...",
                    "distance": 0.123,  # 相似度分数（越小越相关）
                    "score": 8.5        # LLM评分（越高越相关）
                },
                ...
            ]
    Raises:
        ValueError: 如果 `query` 为空或 `limit` 为负数。
    """
    print("folder_filter:", folder_filter)

    # 1. 通过语义搜索获取初步结果
    start_time = time.time()
    results = await semantic_search_hybrid(query, limit, folder_filter, output_fields)
    search_time = time.time() - start_time

    # 2. 初始化打分模型
    start_time = time.time()
    models = [
              ModelSelector.glm4_32b,
              ModelSelector.qwen3_30b_a3b,
              ModelSelector.qwen3_235b_a22b,
              ModelSelector.qwen25_32b
              ]
    models = [BaseLLM(**m, log=False) for m in models]

    # 3. 初始化打分对象 - 使用 data 字段进行匹配
    matchs = [result['data'] for result in results]

    # 4. 进行打分 - 使用 strict 模式
    scores = await ScoreTools.get_scores_by_models(models, query, matchs, match_limit=match_limit)
    score_time = time.time() - start_time

    # 5. 合并结果并排序
    for result, score in zip(results, scores):
        result['score'] = score
    print(f"step2 总耗时: {search_time + score_time:.2f}s || 搜索：{search_time:.2f}s, 打分：{score_time:.2f}s")

    # 6. 设置 distance 字段为 score 的数值的倒数
    for result in results:
        if result['score'] != 0: # 避免 score 为 0 时，distance 为无穷大
            result['distance'] = 1 / result['score']
        else:
            result['distance'] = 1

    return results

async def test_hybrid_search(query: str):
    """测试 hybrid_search 函数"""

    output_fields = ["primary_key", "data"]
    vector = await embeddings_qwen4b.aembedding([query], vector_length=2048, timeout=10)

    results = client_hybrid_bom.hybrid_search(
                        data=vector[0],
                        query=query,
                        output_fields=output_fields,
                        limit=16)
    return results

if __name__ == "__main__":
    query = {
        "型号": "NXA 50V10 5*11",
        "品牌": "SAMYOUNG",
        "参数": "",
        "封装": "插件,D5xL11mm",
        "需求数量": "120",
        "描述": "电解电容"
    }
    # 只使用 key 的值进行搜索
    query = ' '.join([str(v) for k, v in query.items() if v])

    query = "650 V, 166 A, 4.98 nF @ 25 V, 底座，接线柱安装"

    # 1. 测试语义搜索
    # results = asyncio.run(semantic_search_hybrid(query, limit=10, output_fields=["primary_key", "data"]))

    # # 3. 打印结果
    # for i, result in enumerate(results):
    #     print(f"{i+1}. {result['primary_key']}: {result['data'][:500]}...")

    # 2. 测试深度搜索
    print("\n=== 深度搜索结果 ===")
    deep_results = asyncio.run(deep_semantic_search_hybrid(query, limit=10))

    for i, result in enumerate(deep_results):
        print(f"{i+1}. Score: {result['score']:.2f}, Key: {result['primary_key']}, Data: {result['data'][:500]}...")