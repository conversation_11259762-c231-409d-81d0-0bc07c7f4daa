# BOM 匹配搜索系统

一个基于 AI 的智能 BOM（物料清单）匹配搜索系统，支持语义搜索和深度匹配。

## 功能特性

- **语义搜索**: 基于向量相似度的快速搜索
- **深度匹配**: 使用多个 LLM 模型进行智能评分的精准匹配
- **自适应展示**: 根据 BOM 数据的不同字段自动调整显示内容
- **实时搜索**: 支持实时搜索和结果展示

## 技术栈

### 后端
- **FastAPI**: Web API 框架
- **Milvus/Zilliz**: 向量数据库
- **多 LLM 模型**: GLM-4、Qwen2.5 等用于智能评分
- **Embedding**: Qwen3-Embedding-4B 用于向量化

### 前端
- **React 19**: 前端框架
- **TypeScript**: 类型安全
- **Tailwind CSS**: 样式框架
- **shadcn/ui**: UI 组件库
- **Vite**: 构建工具

## 快速开始

### 1. 安装依赖

```bash
# 安装前端依赖
npm install
```

### 2. 启动后端服务

```bash
cd backend
python controller.py
```

后端服务将在 `http://localhost:8848` 启动

### 3. 启动前端服务

```bash
npm run dev
```

前端服务将在 `http://localhost:5173` 启动

## API 接口

### POST /search

搜索 BOM 数据

**请求参数:**
```json
{
  "query": "搜索内容",
  "limit": 32,
  "folder_filter": "",
  "deep_search": false
}
```

**响应格式:**
```json
{
  "results": [
    {
      "primary_key": "folder_0",
      "data": "JSON格式的BOM数据",
      "distance": 0.123,
      "score": 8.5
    }
  ],
  "total": 10,
  "query": "搜索内容",
  "search_type": "semantic"
}
```

## 使用说明

1. **基础搜索**: 在搜索框中输入零件型号、品牌、参数等信息
2. **深度匹配**: 开启"深度匹配"开关，使用 AI 模型进行智能评分
3. **查看结果**: 搜索结果以卡片形式展示，包含相似度和评分信息

## 数据格式

系统支持的 BOM 数据格式为 JSON，包含但不限于：
- 型号 (Model)
- 品牌 (Brand)
- 参数 (Parameters)
- 封装 (Package)
- 描述 (Description)
- 数量 (Quantity)

## 开发说明

- 后端搜索服务位于 `backend/service/search.py`
- 前端主界面位于 `src/App.tsx`
- API 控制器位于 `backend/controller.py`
- UI 组件位于 `src/components/ui/`

## 注意事项

1. 确保 Milvus/Zilliz 服务正常运行
2. 确保 LLM 模型配置正确
3. 首次使用需要初始化向量数据库
4. 深度搜索会消耗更多计算资源和时间
