'''用于存放定时任务'''
import json
import os
import sys
from tqdm import tqdm

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(ROOT_DIR)
print(ROOT_DIR)

def filter_sc_balance_to_file():
    """过滤剩余金额大于0.2的key，输出到对应文件中"""
    import requests
    save_file_path_balance = os.path.join(ROOT_DIR, "siliconflow_balance.key")
    save_file_path = os.path.join(ROOT_DIR, "siliconflow.key")
    with open(os.path.join(ROOT_DIR, "siliconflow_org.key"), "r") as f:
        keys = f.readlines()
    
    available_keys_balance = []
    available_keys = []
    total_balance = 0.0

    url = "https://api.siliconflow.cn/v1/user/info"
    for key in tqdm(keys):
        sc_key = key.strip()
        headers = {"Authorization": f"Bearer {sc_key}"}
        try:
            response = requests.request("GET", url, headers=headers, timeout=10)
            info = json.loads(response.text)
            balance = float(info['data']['balance'])
            status = info['data']['status']

            if status != 'normal':
                continue
            
            if balance > 0.5:
                available_keys_balance.append(sc_key)
                total_balance += balance
            available_keys.append(sc_key)
        except Exception as e:
            print(f"Key {sc_key} 检查失败: {e}")
    if len(available_keys) > 0:
        with open(save_file_path, "w") as f:
            for k in available_keys:
                f.write(k + "\n")
        print(f"可用 key 数量: {len(available_keys)}")
    else:
        print("没有可用 key")

    if len(available_keys_balance) > 0:
        with open(save_file_path_balance, "w") as f:
            for k in available_keys_balance:
                f.write(k + "\n")
        print(f"可用 balance key 数量: {len(available_keys_balance)}")
        print(f"可用余额：{total_balance}")
    else:
        print("没有可用 balance key")

if __name__ == "__main__":
    # 1. 过滤硅基流动key
    filter_sc_balance_to_file()