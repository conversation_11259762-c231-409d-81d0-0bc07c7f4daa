'''
自己封装的 LLM 模型
'''

import os
import sys

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if ROOT_DIR not in sys.path: sys.path.append(ROOT_DIR)

from abc import ABC
from utils.config import config

import requests
import asyncio
import aiohttp
import json
import re
import time

from typing import Optional, Dict, Any, List, Tuple, Union, Callable
from pymilvus import MilvusClient, AnnSearchRequest, RRFRanker

class ModelSelector:
    # 硅基流动免费模型
    glm4_9b = {"model_name": "THUDM/glm-4-9b-chat", "base_url": config.get("BASE_URL_SC"), "api_key": config.get_sc_key(), "is_free": True}
    glm4_9b_0414 = {"model_name": "THUDM/GLM-4-9B-0414", "base_url": config.get("BASE_URL_SC"), "api_key": config.get_sc_key(), "is_free": True}
    intern_7b = {"model_name": "internlm/internlm2_5-7b-chat", "base_url": config.get("BASE_URL_SC"), "api_key": config.get_sc_key(), "is_free": True}

    qwen25_7b = {"model_name": "Qwen/Qwen2.5-7B-Instruct", "base_url": config.get("BASE_URL_SC"), "api_key": config.get("API_KEY_SC"), "is_free": True}
    qwen25_coder_7b = {"model_name": "Qwen/Qwen2.5-Coder-7B-Instruct", "base_url": config.get("BASE_URL_SC"), "api_key": config.get_sc_key(), "is_free": True}

    glm4_9b_z1 = {"model_name": "THUDM/GLM-Z1-9B-0414", "base_url": config.get("BASE_URL_SC"), "api_key": config.get_sc_key(), "is_free": True} # 思考模型
    qwen3_8b = {"model_name": "Qwen/Qwen3-8B", "base_url": config.get("BASE_URL_SC"), "api_key": config.get_sc_key(), "is_free": True} # 思考模型

    # 硅基流动付费模型
    glm4_32b = {"model_name": "THUDM/GLM-4-32B-0414", "base_url": config.get("BASE_URL_SC"), "api_key": config.get("API_KEY_SC")}
    glm4_32b_z1 = {"model_name": "THUDM/GLM-Z1-32B-0414", "base_url": config.get("BASE_URL_SC"), "api_key": config.get("API_KEY_SC")}
    glm45 = {"model_name": "zai-org/GLM-4.5", "base_url": config.get("BASE_URL_SC"), "api_key": config.get("API_KEY_SC")}
    
    qwen3_30b_a3b = {"model_name": "Qwen/Qwen3-30B-A3B-Instruct-2507", "base_url": config.get("BASE_URL_SC"), "api_key": config.get("API_KEY_SC")} # 免费
    qwen3_235b_a22b = {"model_name": "Qwen/Qwen3-235B-A22B-Instruct-2507", "base_url": config.get("BASE_URL_SC"), "api_key": config.get("API_KEY_SC")} # 免费
    qwen3_235b_a22b_think = {"model_name": "Qwen/Qwen3-235B-A22B-Thinking-2507", "base_url": config.get("BASE_URL_SC"), "api_key": config.get("API_KEY_SC")} # 免费
    qwen3_14b = {"model_name": "Qwen/Qwen3-14B", "base_url": config.get("BASE_URL_SC"), "api_key": config.get("API_KEY_SC")} # 免费
    qwen3_32b = {"model_name": "Qwen/Qwen3-32B", "base_url": config.get("BASE_URL_SC"), "api_key": config.get("API_KEY_SC")} # 免费
    qwen_long_32b = {"model_name": "Tongyi-Zhiwen/QwenLong-L1-32B", "base_url": config.get("BASE_URL_SC"), "api_key": config.get("API_KEY_SC")} # 免费

    deepseek_v31 = {"model_name": "deepseek-ai/DeepSeek-V3.1", "base_url": config.get("BASE_URL_SC"), "api_key": config.get("API_KEY_SC"), "enable_thinking": False} # 免费
    deepseek_v31_think = {"model_name": "deepseek-ai/DeepSeek-V3.1", "base_url": config.get("BASE_URL_SC"), "api_key": config.get("API_KEY_SC"), "enable_thinking": True}
    deepseek_v3_sc = {"model_name": "deepseek-ai/DeepSeek-V3", "base_url": config.get("BASE_URL_SC"), "api_key": config.get("API_KEY_SC")} # 免费
    deepseek_r1_sc = {"model_name": "deepseek-ai/DeepSeek-R1", "base_url": config.get("BASE_URL_SC"), "api_key": config.get("API_KEY_SC")} # 免费
    kimi_k2 = {"model_name": "moonshotai/Kimi-K2-Instruct", "base_url": config.get("BASE_URL_SC"), "api_key": config.get("API_KEY_SC")} # 免费
    
    qwen25_14b = {"model_name": "Qwen/Qwen2.5-14B-Instruct", "base_url": config.get("BASE_URL_SC"), "api_key": config.get("API_KEY_SC")}
    qwen25_32b = {"model_name": "Qwen/Qwen2.5-32B-Instruct", "base_url": config.get("BASE_URL_SC"), "api_key": config.get("API_KEY_SC")}
    qwen25_72b = {"model_name": "Qwen/Qwen2.5-72B-Instruct", "base_url": config.get("BASE_URL_SC"), "api_key": config.get("API_KEY_SC")} # 1.0/1.0
    qwen25_coder_32b = {"model_name": "Qwen/Qwen2.5-Coder-32B-Instruct", "base_url": config.get("BASE_URL_SC"), "api_key": config.get("API_KEY_SC")}
    
    # 阿里模型
    qwen_plus_0112 = {"model_name": "qwen-plus-2025-01-12", "base_url": config.get("BASE_URL_AL"), "api_key": config.get("API_KEY_AL")} # 0.8/2.0
    qwen_turbo_1220 = {"model_name": "qwen-turbo", "base_url": config.get("BASE_URL_AL"), "api_key": config.get("API_KEY_AL")} # 0.3/0.6
    qwen_turbo_1220 = {"model_name": "qwen-turbo", "base_url": config.get("BASE_URL_AL"), "api_key": config.get("API_KEY_AL")} # 0.3/0.6

    # 云雾模型
    gpt4o = {"model_name": "gpt-4o", "base_url": config.get("BASE_URL_YW"), "api_key": config.get("API_KEY_YW_GPT")}
    gpt41 = {"model_name": "gpt-4.1-2025-04-14", "base_url": config.get("BASE_URL_YW"), "api_key": config.get("API_KEY_YW_GPT")}
    o4_mini = {"model_name": "o4-mini", "base_url": config.get("BASE_URL_YW"), "api_key": config.get("API_KEY_YW_GPT")}

    claude4_sonnet = {"model_name": "claude-sonnet-4-20250514", "base_url": config.get("BASE_URL_YW"), "api_key": config.get("API_KEY_YW_Claude")}
    gemini25_flash = {"model_name": "gemini-2.5-flash", "base_url": config.get("BASE_URL_YW"), "api_key": config.get("API_KEY_YW_GEMINI")}
    gemini25_pro = {"model_name": "gemini-2.5-pro", "base_url": config.get("BASE_URL_YW"), "api_key": config.get("API_KEY_YW_GEMINI")}
    gpt5 = {"model_name": "gpt-5-2025-08-07", "base_url": config.get("BASE_URL_YW"), "api_key": config.get("API_KEY_YW_GPT")}
    
    # 豆包模型
    doubao15_pro = {"model_name": "ep-20250202164144-8z9gj", "base_url": config.get("BASE_URL_DB"), "api_key": config.get("API_KEY_DB")} # 0.8/2.0
    doubao15_lite = {"model_name": "ep-20250202164232-qv7s4", "base_url": config.get("BASE_URL_DB"), "api_key": config.get("API_KEY_DB")} # 0.3/0.6
    deepseek_v3 = {"model_name": "ep-m-20250326231821-9jwrq", "base_url": config.get("BASE_URL_DB"), "api_key": config.get("API_KEY_DB")} # 2.0/8.0
    deepseek_r1 = {"model_name": "ep-m-20250220224844-md6jm", "base_url": config.get("BASE_URL_DB"), "api_key": config.get("API_KEY_DB")} # 2.0/8.0

    # 智谱模型
    glm4_flash = {"model_name": "GLM-4-Flash", "base_url": config.get("BASE_URL_GLM"), "api_key": config.get("API_KEY_GLM"), "is_free": True} # 免费
    glm4v_flash = {"model_name": "GLM-4V-Flash", "base_url": config.get("BASE_URL_GLM"), "api_key": config.get("API_KEY_GLM"), "is_free": True} # 免费
    glm4_air = {"model_name": "GLM-4-Air-0111", "base_url": config.get("BASE_URL_GLM"), "api_key": config.get("API_KEY_GLM")} # 0.5/0.5

    # Embedding 模型
    zp_embedding_v3 = {"model_name": "Embedding-3", "base_url": config.get("BASE_URL_GLM_EMBEDDING"), "api_key": config.get("API_KEY_GLM")} # 0.5
    sc_bge_m3 = {"model_name": "BAAI/bge-m3", "base_url": config.get("BASE_URL_SC") + '/embeddings', "api_key": config.get("API_KEY_SC"), "is_free": True} # 免费
    sc_bge_m3_pro = {"model_name": "Pro/BAAI/bge-m3", "base_url": config.get("BASE_URL_SC") + '/embeddings', "api_key": config.get("API_KEY_SC")} # 0.05
    sc_qwen3_embedding_4b = {"model_name": "Qwen/Qwen3-Embedding-4B", "base_url": config.get("BASE_URL_SC") + '/embeddings', "api_key": config.get("API_KEY_SC")} # 0.05
    gpt_embedding_small = {"model_name": "text-embedding-3-small", "base_url": config.get("BASE_URL_YW") + '/embeddings', "api_key": config.get("API_KEY_YW")}

    # Rerank 模型
    bge_rerank = {"model_name": "BAAI/bge-reranker-v2-m3", "base_url": config.get("BASE_URL_SC") + '/rerank', "api_key": config.get("API_KEY_SC"), "is_free": True} # 免费
    bge_rerank_pro = {"model_name": "Pro/BAAI/bge-reranker-v2-m3", "base_url": config.get("BASE_URL_SC") + '/rerank', "api_key": config.get("API_KEY_SC")} # 0.05
    qwen8b_rerank = {"model_name": "Qwen/Qwen3-Reranker-8B", "base_url": config.get("BASE_URL_SC") + '/rerank', "api_key": config.get("API_KEY_SC")} # 0.05
    qwen4b_rerank = {"model_name": "Qwen/Qwen3-Reranker-4B", "base_url": config.get("BASE_URL_SC") + '/rerank', "api_key": config.get("API_KEY_SC")} # 0.05
    glm_rerank = {"model_name": "rerank", "base_url": config.get("BASE_URL_GLM") + '/rerank', "api_key": config.get("API_KEY_GLM")} # 免费

    # zilliz 数据库
    hybrid_bom = {"url": config.get('zilliz_uri_bom'), "token": config.get('zilliz_token'), "collection_name": "bom_hybrid"}

class BaseLLM(ABC):

    ''' Language Model Strategy

    所有 LLM model 的基类，用于：
    1. 定义生成文本的接口

    '''
    def __init__(self, model_name: str, base_url: str, api_key: str, log: bool=True, is_free: bool=False, enable_thinking: bool=None):
        self.base_url = base_url + '/chat/completions'
        self.api_key = api_key
        self.name = model_name
        self.is_free = is_free
        self.enable_thinking = enable_thinking
        if log:
            print(f"初始化 {self.name} 成功 ~~~")

    def __copy__(self):
        """实现浅拷贝"""
        new_one = type(self)(self.name, self.base_url.replace('/chat/completions', ''), self.api_key, log=False)
        return new_one

    def _get_payload(self, 
        prompt: str, 
        system_prompt: str, 
        temperature: float,
        max_tokens: int=8192,
        history_messages: List[Dict[str, str]]=[],
        stream: bool=False,
        frequency_penalty: float=0.5,
        top_p: float=0.7,
        top_k: int=50,
        n: int=1,
        response_format: Dict[str, str]={"type": "text"},
        tools: Optional[List[Dict[str, Any]]] = None,
    ) -> Dict[str, Any]:
        """构建请求payload"""
        message_list = [{"role": "system", "content": system_prompt}]
        message_list.extend(history_messages)
        message_list.append({"role": "user", "content": prompt})

        if 'gemini' in self.name:
            max_tokens = max_tokens * 2

        payload = {
            "model": self.name,
            "messages": message_list,
            "stream": stream,
            "max_tokens": max_tokens,
            "temperature": temperature,
            # "top_p": top_p,
            # "top_k": top_k,
            # "frequency_penalty": frequency_penalty,
            "n": n,
            "response_format": response_format,
        }

        # 如果 self.name 包含 gpt 字样，或者 o4 字样，删除 top_k 参数
        if 'gpt' in self.name.lower() or 'o4' in self.name.lower():
            payload.pop("top_k", None)
        if self.enable_thinking:
            payload["enable_thinking"] = self.enable_thinking

        if tools:
            payload["tools"] = tools

        return payload

    def _get_headers(self) -> Dict[str, str]:
        """构建请求headers"""
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

    def _process_response_content(self, content: str) -> str:
        """处理响应内容"""
        try:
            parsed_content = json.loads(content)
            if isinstance(parsed_content, dict):
                # 按优先级尝试不同的字段名
                for field in ['text', 'message', 'content']:
                    if field in parsed_content:
                        return parsed_content[field]
                return content
            return content
        except json.JSONDecodeError:
            return content

    def ask(self, prompt: str, system_prompt: str="you are a helpful assistant", temperature: float=0.6, history_messages: List[Dict[str, str]]=[]) -> str:
        """同步方式调用LLM"""
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # 如果已经在事件循环中，创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(self.ask_async(prompt, system_prompt, temperature, history_messages=history_messages))
        finally:
            if not loop.is_running():
                loop.close()

    async def ask_async(self, 
        prompt: str, 
        system_prompt: str="you are a helpful assistant", 
        temperature: float=0.6, 
        max_tokens: int=4096, 
        history_messages: List[Dict[str, str]]=[],
        top_k: int=50,
        top_p: float=0.7,
        frequency_penalty: float=0.5,
    ) -> str:
        """异步方式调用LLM"""
        payload = self._get_payload(prompt, system_prompt, temperature, max_tokens=max_tokens, history_messages=history_messages, top_k=top_k, top_p=top_p, frequency_penalty=frequency_penalty)
        headers = self._get_headers()
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(self.base_url, json=payload, headers=headers) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"API请求失败: {response.status}, {error_text}")
                    
                    response_json = await response.json()
                    content = response_json['choices'][0]['message']['content']
                    return self._process_response_content(content)
                    
        except aiohttp.ClientError as e:
            print(f"网络请求错误: {str(e)}")
            raise
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {str(e)}")
            raise
        except KeyError as e:
            print(f"响应格式错误，缺少必要字段: {str(e)}")
            raise
        except Exception as e:
            print(f"未知错误: {str(e)}")
            raise

    async def ask_multi_async(self, prompts: list[str], system_prompt: str="you are a helpful assistant", temperature: float=0.6, history_messages: List[Dict[str, str]]=[]) -> list[str]:
        """异步方式调用LLM"""
        tasks = [self.ask_async(prompt, system_prompt, temperature, history_messages=history_messages) for prompt in prompts]
        return await asyncio.gather(*tasks)

    def deal_think(self, output: str, stream: bool = False, state: dict = None):
        """处理思考内容，分离思考部分和正文部分（支持流式和非流式模式）
        
        Args:
            output (str): 原始输出内容或流式片段
            stream (bool): 是否为流式处理
            state (dict): 流式状态管理（外部传入，需包含 is_in_think_mode, accumulated_content）
        Returns:
            - 非流式: (think_content, main_content)
            - 流式: 生成器，yield 每个片段
        """
        import re
        if not stream:
            # 非流式处理，原有逻辑
            if self._is_op_model():
                think_pattern = r'<think>(.*?)</think>'
                think_matches = re.findall(think_pattern, output, re.DOTALL)
                if think_matches:
                    think_content = '\n'.join(think_matches)
                    main_content = re.sub(think_pattern, '', output, flags=re.DOTALL).strip()
                    return (think_content, main_content)
                else:
                    return (None, output)
            return (None, output)
        else:
            # 流式处理，需用 state 记录上下文
            if not self._is_op_model():
                # 非 op 模型，直接 yield 内容
                yield output
                return
            # op 模型流式处理
            if state is None:
                raise ValueError("流式模式下必须传入 state 字典")
            # 状态变量
            is_in_think_mode = state.get('is_in_think_mode', False)
            accumulated_content = state.get('accumulated_content', '')
            content = output
            # 累积内容
            accumulated_content += content
            # 检查思考模式的开始和结束
            if '<think>' in accumulated_content and not is_in_think_mode:
                is_in_think_mode = True
                before_think = accumulated_content.split('<think>', 1)[0]
                if before_think.strip():
                    yield before_think
                accumulated_content = accumulated_content.split('<think>', 1)[1]
                state['is_in_think_mode'] = is_in_think_mode
                state['accumulated_content'] = accumulated_content
                return
            if '</think>' in accumulated_content and is_in_think_mode:
                think_content, after_think = accumulated_content.split('</think>', 1)
                if think_content.strip():
                    yield f"<think>{think_content}</think>"
                is_in_think_mode = False
                if after_think.strip():
                    yield after_think
                accumulated_content = ''
                state['is_in_think_mode'] = is_in_think_mode
                state['accumulated_content'] = accumulated_content
                return
            if is_in_think_mode:
                if content.strip():
                    yield f"<think>{content}</think>"
                state['is_in_think_mode'] = is_in_think_mode
                state['accumulated_content'] = accumulated_content
                return
            if accumulated_content:
                yield accumulated_content
                accumulated_content = ''
            state['is_in_think_mode'] = is_in_think_mode
            state['accumulated_content'] = accumulated_content
            return

    def _is_op_model(self) -> bool:
        """判断是否为 op 系列模型
        
        Returns:
            bool: 如果是 op 模型返回 True，否则返回 False
        """
        # 通过 base_url 判断是否为 op 模型
        return 'ppinfra.com' in self.base_url or 'deepseek' in self.base_url

    async def ask_async_stream(self, prompt: str, system_prompt: str="you are a helpful assistant", history_messages: List[Dict[str, str]]=[],  temperature: float=0.6):
        """异步流式方式调用LLM
        
        参数:
            prompt (str): 用户输入的提示文本
            system_prompt (str): 系统提示文本
            temperature (float): 温度参数，控制输出的随机性
            
        返回:
            异步生成器，可以逐步获取模型的输出内容
        """
        
        message_list = [{"role": "system", "content": system_prompt}]
        message_list.extend(history_messages)
        message_list.append({"role": "user", "content": prompt})

        # 构建请求payload，与_get_payload类似但启用stream
        payload = self._get_payload(prompt, system_prompt, temperature, stream=True, history_messages=history_messages)
        
        # 构建请求headers
        headers = self._get_headers()
        
        # 流式处理状态管理（仅对 op 模型有效）
        state = {'is_in_think_mode': False, 'accumulated_content': ''}
        
        async with aiohttp.ClientSession() as session:
            async with session.post(self.base_url, json=payload, headers=headers) as response:
                # 检查响应状态
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"API请求失败: {response.status}, {error_text}")
                
                # 处理流式响应
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    
                    # 跳过空行
                    if not line: continue
                    
                    # 跳过data: [DONE]行
                    if line == "data: [DONE]": break
                    
                    # 处理数据行
                    if line.startswith("data: "):
                        try:
                            # 提取JSON部分
                            json_str = line[6:]  # 去掉"data: "前缀
                            data = json.loads(json_str)
                            
                            # 提取内容
                            if "choices" in data and len(data["choices"]) > 0:
                                choice = data["choices"][0]
                                if "delta" in choice:
                                    content = None
                                    
                                    # 优先处理思考内容（针对支持 reasoning_content 的模型）
                                    if "reasoning_content" in choice["delta"] and choice["delta"]["reasoning_content"]:
                                        content = choice["delta"]["reasoning_content"]
                                        if content:
                                            for chunk in self.deal_think(content, stream=True, state=state):
                                                yield f"<think>{chunk}</think>"
                                            continue
                                    
                                    # 获取普通内容
                                    elif "content" in choice["delta"] and choice["delta"]["content"]:
                                        content = choice["delta"]["content"]
                                    
                                    # 如果有内容，处理 op 模型的流式思考逻辑
                                    # print(f"content: {content}")
                                    if content:
                                        if self._is_op_model():
                                            # 统一交给 deal_think 处理
                                            for chunk in self.deal_think(content, stream=True, state=state):
                                                yield chunk
                                        else:
                                            yield content
                        except json.JSONDecodeError:
                            # 忽略无法解析的JSON
                            continue
                        except Exception as e:
                            print(f"处理流式响应时出错: {e}")
                            continue
                
                # 处理剩余的累积内容
                if state['accumulated_content']:
                    if state['is_in_think_mode']:
                        for chunk in self.deal_think(state['accumulated_content'], stream=True, state=state):
                            yield chunk

class ToolLLM(BaseLLM):
    def __init__(self, model_name: str, base_url: str, api_key: str):
        super().__init__(model_name, base_url, api_key)
        self.tools = []
    
    def register_tool(self, tool: Callable):
        self.tools.append(tool)

    async def ask_async(self, prompt: str, 
        system_prompt: str="you are a helpful assistant", 
        temperature: float=0.6, 
        max_tokens: int=4096, 
        tools: Optional[List[Dict[str, Any]]] = None,
        history_messages: List[Dict[str, str]]=[]
    ) -> Dict[str, Any]:
        """异步方式调用LLM，支持工具调用并处理响应"""
        payload = self._get_payload(prompt, system_prompt, temperature, max_tokens=max_tokens, tools=tools, history_messages=history_messages)
        headers = self._get_headers()
        
        async with aiohttp.ClientSession() as session:
            async with session.post(self.base_url, json=payload, headers=headers) as response:
                response_json = await response.json()
                
                # 检查是否有错误
                if response.status != 200:
                    raise Exception(f"API请求失败: {response.status}, {response_json}")

                # 检查响应中是否包含 tool_calls
                choice = response_json['choices'][0]
                if 'message' in choice and 'tool_calls' in choice['message'] and choice['message']['tool_calls']:
                    return {"type": "tool_call", "calls": choice['message']['tool_calls']}
                else:
                    # 否则，返回普通文本内容
                    content = choice['message']['content']
                    processed_content = self._process_response_content(content)
                    return {"type": "text", "content": processed_content}

    def ask_by_tools(self, prompt: str, system_prompt: str="you are a helpful assistant", temperature: float=0.6, tools: Optional[List[Dict[str, Any]]] = None, history_messages: List[Dict[str, str]]=[]) -> Dict[str, Any]:
        """同步方式调用LLM，支持工具调用
        
        返回:
            Dict[str, Any]: 包含响应类型 ('text' 或 'tool_call') 和对应内容
        """
        loop = asyncio.get_event_loop()
        if loop.is_running():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        try:
            # 注意：传递 tools 参数给 ask_async
            return loop.run_until_complete(self.ask_async(prompt, system_prompt, temperature, tools=tools, history_messages=history_messages))
        finally:
            if not loop.is_running():
                loop.close()

    async def ask_async_stream_with_tools(self, prompt: str, system_prompt: str="you are a helpful assistant", history_messages: List[Dict[str, str]]=[], tools: Optional[List[Dict[str, Any]]] = None, temperature: float=0.6):
        """异步流式方式调用LLM，支持工具调用

        Args:
            prompt (str): 用户输入的提示文本
            system_prompt (str): 系统提示文本
            history_messages (List[Dict[str, str]]): 历史消息列表
            tools (Optional[List[Dict[str, Any]]]): 可选的工具列表
            temperature (float): 温度参数

        Returns:
            异步生成器，产生包含类型和内容的字典:
            - {"type": "text", "content": "..."}
            - {"type": "think", "content": "..."}
            - {"type": "tool_call", "calls": [...]} (在流结束后返回完整的工具调用)
        """
        message_list = [{"role": "system", "content": system_prompt}]
        message_list.extend(history_messages)
        message_list.append({"role": "user", "content": prompt})

        # 构建请求payload
        payload = {
            "model": self.name,
            "messages": message_list,
            "stream": True, # 启用流式输出
            "stop": ["null"],
            "temperature": temperature,
            "top_p": 0.7,
            "top_k": 50,
            "frequency_penalty": 0.5,
            "n": 1,
            "response_format": { "type": "text" }, # 或根据需要设为 "auto"
        }
        if tools:
            payload["tools"] = tools
            # 可能需要调整 response_format if tools are present, e.g., {"type": "auto"}
            # payload["response_format"] = {"type": "auto"}

        # 构建请求headers
        headers = self._get_headers()

        accumulated_tool_calls = {} # {index: {"id": ..., "type": ..., "function": {"name": ..., "arguments": ""}}}

        async with aiohttp.ClientSession() as session:
            async with session.post(self.base_url, json=payload, headers=headers) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"API请求失败: {response.status}, {error_text}")

                async for line in response.content:
                    line = line.decode('utf-8').strip()

                    if not line:
                        continue

                    if line == "data: [DONE]":
                        break

                    if line.startswith("data: "):
                        try:
                            json_str = line[6:]
                            data = json.loads(json_str)

                            if "choices" in data and len(data["choices"]) > 0:
                                delta = data["choices"][0].get("delta", {})

                                # 1. 处理普通内容
                                if "content" in delta and delta["content"]:
                                    yield {"type": "text", "content": delta["content"]}

                                # 2. 处理思考内容
                                elif "reasoning_content" in delta and delta["reasoning_content"]:
                                     yield {"type": "think", "content": delta["reasoning_content"]}

                                # 3. 处理工具调用片段
                                elif "tool_calls" in delta:
                                    for tool_call_chunk in delta["tool_calls"]:
                                        index = tool_call_chunk.get("index") # Qwen uses index
                                        if index is None and "id" in tool_call_chunk: # OpenAI uses id for matching? Check API spec. Assume index first.
                                             # Fallback or alternative handling if index isn't primary key
                                             pass

                                        if index is not None:
                                            if index not in accumulated_tool_calls:
                                                accumulated_tool_calls[index] = {"id": None, "type": None, "function": {"name": None, "arguments": ""}} # 更通用的初始化

                                            call_data = accumulated_tool_calls[index]

                                            # Update fields if present in the chunk
                                            if "id" in tool_call_chunk and tool_call_chunk["id"]:
                                                call_data["id"] = tool_call_chunk["id"]
                                            if "type" in tool_call_chunk and tool_call_chunk["type"]:
                                                call_data["type"] = tool_call_chunk["type"] # 应捕获 'function'
                                            if "function" in tool_call_chunk:
                                                func_chunk = tool_call_chunk["function"]
                                                if "name" in func_chunk and func_chunk["name"]:
                                                    call_data["function"]["name"] = func_chunk["name"]
                                                if "arguments" in func_chunk and func_chunk["arguments"]:
                                                    call_data["function"]["arguments"] += func_chunk["arguments"]

                        except json.JSONDecodeError:
                            print(f"忽略无法解析的JSON: {line}")
                            continue
                        except Exception as e:
                            print(f"处理流式响应时出错: {e}")
                            continue

                # 流结束后，如果收集到了工具调用信息，则统一返回
                if accumulated_tool_calls:
                    # Sort by index and convert to list, stripping arguments
                    final_calls = []
                    for i in sorted(accumulated_tool_calls.keys()):
                        call = accumulated_tool_calls[i]
                        if call.get("function") and "arguments" in call["function"]:
                             call["function"]["arguments"] = call["function"]["arguments"].strip()
                        final_calls.append(call)

                    yield {"type": "tool_call", "calls": final_calls}

class BaseEmbedding(ABC):
    '''
        这里所有函数 embedding 结果返回的都是一个 list，接受的也是 list
        可以使用 [0] 取第一个结果
    '''
    def __init__(self, model_name: str, base_url: str, api_key: str, is_free: bool=False):
        self.base_url = base_url
        self.api_key = api_key
        self.model = model_name
        self.is_free = is_free

        print(f"初始化嵌入模型 {self.model} 成功 ~~~")

    def embedding(self, text_list, vector_length: int=2048, timeout: int=15):
        """
        通过 ZHIPU AI OPEN PLATFORM 获取文本的 Embedding 向量。

        参数:
            text_list (list[str]): 需要获取嵌入的文本列表。
            vector_length (int): 输出向量的长度。
            timeout (int): 请求超时时间（秒）。

        返回:
            list: 每个文本对应的 Embedding 向量。
        """

        # 构造请求数据
        payload = {
            "model": self.model,
            "input": text_list,
            "dimensions": vector_length
        }

        # 设置请求头
        headers = {
            "Content-Type": "application/json",
            # 如果有 API 密钥或其他认证信息，请在此处添加
            "Authorization": f"Bearer {self.api_key}"
        }

        # 发送 POST 请求
        try:
            response = requests.post(self.base_url, 
                                     headers=headers, 
                                     json=payload, 
                                     timeout=timeout)
            response.raise_for_status()  # 检查请求是否成功

            # 解析响应结果
            results = response.json()['data']

            # 如果 results 长度大于 1，就按照 index 从小到大排序
            if len(results) > 1:
                results = sorted(results, key=lambda x: x['index'])

            embeddings = [item['embedding'] for item in results]

            return embeddings
        except requests.exceptions.Timeout:
            print(f"请求超时：{timeout}秒")
            return []
        except requests.exceptions.RequestException as e:
            print(f"请求失败：{e}")
            return []

    async def aembedding(self, text_list, vector_length: int=1024, timeout: int=15):
        """
        异步方式获取文本的 Embedding 向量

        参数:
            text_list (list[str]): 需要获取嵌入的文本列表
            vector_length (int): 输出向量的长度
            timeout (int): 请求超时时间（秒）

        返回:
            list: 每个文本对应的 Embedding 向量
        """
        # 构造请求数据
        payload = {
            "model": self.model,
            "input": text_list,
            "dimensions": vector_length
        }

        # 设置请求头
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        # 使用 aiohttp 发送异步请求
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(self.base_url, json=payload, headers=headers, timeout=timeout) as response:
                    response.raise_for_status()  # 检查请求是否成功
                    results = await response.json()
                    results = results['data']

                    # 如果 results 长度大于 1，就按照 index 从小到大排序（防止返回乱序）
                    if len(results) > 1:
                        results = sorted(results, key=lambda x: x['index'])

                    embeddings = [item['embedding'] for item in results]
                    return embeddings
        # 超时
        except asyncio.TimeoutError:
            print(f"请求超时：{timeout}秒")
            return []
        except Exception as e:
            print(f"异步请求失败：{e}")
            return []

class BaseRerank(ABC):
    def __init__(self, model_name: str, base_url: str, api_key: str, is_free: bool=False):
        self.base_url = base_url
        self.api_key = api_key
        self.model = model_name
        self.is_free = is_free

    def rerank(self, query: str, doc_list: list, top_n: int=64, 
               instruction: str="You are an academic literature evaluation expert. Given a user-provided query topic and a candidate paper, you must strictly assess the semantic relevance between the two."):
        """
        通过 ZHIPU AI OPEN PLATFORM 获取文本的 Embedding 向量。

        参数:
            query: 查询
            doc_list: 和查询对应的需要重排序的文档
            top_n: 返回重排序后结果的前多少个内容

        返回:
            list: 重排序后的前 top_n 个结果

        """

        # 构造请求数据
        payload = {
            "model": self.model,
            "instruction": instruction,
            "query": query,
            "documents": doc_list,
            "top_n": top_n,
            "return_documents": False,
            # "max_chunks_per_doc": 1024,
            # "overlap_tokens": 80
        }

        # 设置请求头
        headers = {
            "Content-Type": "application/json",
            # 如果有 API 密钥或其他认证信息，请在此处添加
            "Authorization": f"Bearer {self.api_key}"
        }

        # 发送 POST 请求
        try:
            response = requests.post(self.base_url, headers=headers, json=payload, timeout=60)
            response.raise_for_status()  # 检查请求是否成功

            # 解析响应结果
            results = response.json()['results']
            results = [{'index': result['index'], 'score': result['relevance_score']} for result in results]

            # 如果 results 长度大于 1，就获取 index 对应的重排序后的文档
            # if len(results) > 1:
            #     rerank_docs = [doc_list[info['index']] for info in results]

            # [{'index': x, 'score': x}]
            return results
        except requests.exceptions.RequestException as e:
            print(f"请求失败：{e}")
            return []

class BasePrompt(ABC):

    simple_translate_2cn = """ Translate the following English text into Chinese """

    simple_translate_2en = """ Translate the following Chinese text into English """

    translate_base = """
        你是一位专业的学术翻译专家，负责将英文学术内容翻译成中文。

        翻译要求：
        1. 翻译结果要符合中文表达习惯，流畅自然
        2. 对技术缩写等专有名词可保留英文原文（例如标题开头的简短的词组或缩写）
        3. 对网址、邮箱、数学公式等无需翻译的内容，应保留英文原文
        3. 直接输出翻译结果，不要添加任何解释或额外内容
        4. 翻译结果最外侧不要添加引号或括号

        需要翻译的内容如下：
    """

    translate_mid = """
    你是一个翻译专家，请将用户给定的查询语句精简翻译为英文短语，并注意以下几点:
    1. 如果对应多个专有名词，请使用逗号隔开
    2. 如果包含有缩写等内容，请合理使用少量的关键词进行扩充以便于语义匹配
    3. 如果无法翻译（缩写等没有直接对应翻译对象的内容），或者原文全部是英文，只需要输出 "--" 即可
    4. 直接输出结果，不要输出任何解释
    5. 输出长度控制在 30 个单词以内
    6. 不要输出任何类似 用户: xx， 系统: xx 的格式，直接输出结果
    """

    abstract_refinement_base = """
    你是一个信息总结专家，对中英文有深入的了解，
    能够深入分析所给英文内容的含义，可以将准备回复给用户的中文内容表示的流畅且符合中文语法习惯。
    用户将会自己的搜索词 query 以及搜索结果中的论文 abstract 发给你，
    请你使用尽可能简短的中文按照下列要求对围绕用户的查询目的 (query), 给对应 abstract 进行精炼总结：
    
    重要提示：
    1. 直接返回结果，不要添加任何其他文本、注释或说明，尤其是不要在回答前边加上系统，用户等角色称呼
    2. 不要使用任何引号、大括号、特殊字符，不要包含任何 url
    3. 可以使用如下类似格式以尽量保持简洁："在 xxx (问题或者领域), 提出 xxx (方法)，达到了 xxx (效果)"
    4. 确保生成的内容是单行的，没有任何换行、缩进、空格等字符
    5. 不要在内容中使用任何转义字符或 unicode 字符
    6. 字数不超过 50 字
    7. 内容必须同时满足：(1) 优先呼应query的搜索意图, (2)严格基于abstract客观内容
    8. 尽可能将精炼结果向 query 靠近，但是要考虑 abstract 和 query 是否相关，不相关的话也没必要强行靠近
    9. 如果 abstract 和 query 完全不相关，请直接输出精炼结果即可，无需考虑 query 的搜索意图，切记不要强行将 abstract 和 query 进行关联
    """

    query_extend_cn = """
    你是一个搜索关键词扩展专家，擅长将用户的搜索意图转化为多个相关的搜索词或短语。
    用户会输入一段描述他们搜索需求的文本，请你生成与之相关的关键词列表。

    重要提示：
    1. 关键词应该包含同义词、近义词、上位词、下位词等
    2. 所有内容必须与原始搜索意图高度相关
    3. 所有内容必须使用英文表达
    4. 直接返回使用逗号隔开的英文关键词列表，不要添加任何其他文本、注释或标记。
    5. 拓展后的词组数量不超过 3 个，每个词组不超过 4 个单词。
    """

    query_translate = """
    请你将用户输入的任何查询语句翻译为英文，并遵循以下规则：
    1. 保留原文中的英文术语，多个专有名词用逗号分隔
    2. 必要时对缩写进行括号补充说明 (例如: WHO -> WHO (World Health Organization))
    3. 保留无法翻译的内容 (如缩写、首字母缩略词等)
    4. 若原文全部为英文，则直接输出原文
    5. 优先保证语义准确性，而非逐字翻译
    6. 仅输出翻译结果，不添加任何解释
    7. 保持原始查询的结构和关键词
    """

    literature_summary_prompt = """
    你是一位学术文献综述专家。请根据用户提供的分类指引，生成一段简洁的文献综述摘要。

    **任务要求**：
    1. 按照类别组织文献，创建一个结构化的综述
    2. 每个类别下对每篇文章进行简要说明

    **输出格式**：
    生成一个结构化的文献综述，包含：
    1. 根据已有的文献做简短的研究领域概述
    2. 每个类别下的每篇文章的主要发现和贡献（字数少于四十字，方便读者速览）
    3. 输出示例：

    本研究领域主要关注[简短描述研究领域]。近年来，该领域的研究呈现出[描述研究趋势]的特点。
    ## 一、[类别名称]
    - [1] 提出了[主要贡献]，该研究通过[方法]解决了[问题]。
    - [3] 进一步探索了[研究方向]，其创新点在于[创新点]。

    ## 二、[类别名称]
    - [2] 开发了[技术/方法]，该方法在[应用场景]中表现出色。
    - [4] 通过[方法]解决了[问题]，实验结果表明[主要发现]。

    ## 三、[类别名称]
    - [5] 提出了新的[理论/框架]，为[研究方向]提供了新视角。
    - [6] 比较了不同的[方法/技术]，发现[主要结论]。

    """

    literature_classify = """
    你是一位专业的文献分类专家。请根据用户提供的文献检索时所使用的 query ，对检索结果中的多篇文献内容，以及进行分类整理。

    **任务要求**：
    1. 仔细阅读所有提供的文献信息（标题和摘要）
    2. 根据文献内容创建2-4个有意义的分类类别！！！，不要超过4个！！！
    3. 确保各个分类之间有明显的差异性
    4. 每个分类应当准确反映其下文献的核心主题

    **输入格式**：
    文献将以以下格式提供：
    用户 query
    [1] 标题1 && 摘要1
    [2] 标题2 && 摘要2
    ...

    **输出格式**：
    请按以下格式输出分类结果：

    ## 分类建议

    【分类名称1】: [1][3][5]...
    【分类名称2】: [2][4][6]...
    【分类名称3】: [7][8]...
    ...

    **注意事项**：
    - 分类名称应简洁明确，能够概括该类别下文献的共同特点
    - 确保每篇文献都被分类，不遗漏任何文献
    - 如果某篇文献跨越多个类别，可以将其放在最相关的类别中
    - 分类数量应根据文献内容自然形成，通常为2-4个类别，避免文献为 1 个的类别
    - 不需要添加任何的分类解释
    """

    # =======================================================

class BaseZilliz(ABC):
    def __init__(self, url: str, token: str, collection_name: str):
        self.url = url
        self.token = token
        self.collection_name = collection_name
        
        client = MilvusClient(uri=url, token=token)
        client.describe_collection(collection_name)

        self.client = client

    def mult_search(
        self,
        data: Union[List[list], list],
        output_fields: Optional[List[str]] = None,
        limit: int = 10,
        offset: int = 0,
        filter: str = "",
        **kwargs,
    ) -> List[List[dict]]:
        ''' 处理多个向量的搜索，返回多个结果 list '''
        return self.client.search(collection_name=self.collection_name,
                                   data=data,
                                   filter=filter,
                                   limit=limit,
                                   output_fields=output_fields,
                                   offset=offset,
                                   **kwargs)
    
    def search(
        self,
        data: list,
        output_fields: Optional[List[str]] = None,
        limit: int = 10,
        offset: int = 0,
        filter: str = "",
        **kwargs,
    ) -> List[dict]:
        ''' 处理单个向量的搜索，返回单个结果 list '''
        return self.client.search(collection_name=self.collection_name,
                                   data=[data],
                                   filter=filter,
                                   limit=limit,
                                   output_fields=output_fields,
                                   offset=offset,
                                   **kwargs)[0]

    def hybrid_search(self, data: list, query: str, output_fields: Optional[List[str]] = None, limit: int = 10, expr: str = "", **kwargs):
        '''
        Args:
            data: 稠密向量 (list)
            query: 用户 query (所有英文字母，忽略中文) , milvus 会自动转换为稀疏向量并匹配
            output_fields: 输出字段
            limit: 限制返回数量
            offset: 偏移量
            expr: 过滤条件 (等价于 filter 参数)
            **kwargs: 其他参数
        '''

        # 创建稠密向量搜索请求
        req_dense = AnnSearchRequest(data=[data], 
                    anns_field="vector",
                    expr=expr,
                    param={"metric_type": "COSINE"},
                    limit=limit)

        # 创建稀疏向量搜索请求 (尽可能少一点)
        # clean_query = re.sub(r'[^A-Za-z]+', ' ', query).strip()
        clean_query = query.strip()
        req_sparse = AnnSearchRequest(data=[clean_query], 
                    anns_field="sparse_vector", 
                    expr=expr, 
                    param={"metric_type": "BM25"},
                    limit=limit//2 if limit//2 > 6 else 6)

        # 执行混合搜索
        results = self.client.hybrid_search(
            collection_name=self.collection_name,
            reqs=[req_dense, req_sparse],
            ranker=RRFRanker(),
            limit=limit,
            output_fields=output_fields
        )

        return results[0]

async def test_async():
    """测试异步调用"""
    print("开始异步测试")
    base_llm = BaseLLM(**ModelSelector.qwen3_30b_a3b)
    out = await base_llm.ask_async("你是谁")
    print("异步调用成功")
    print(out)

def test_sync():
    """测试同步调用"""
    print("\n开始同步测试")
    base_llm = BaseLLM(**ModelSelector.qwen3_8b)
    out = base_llm.ask("你是谁")
    print("同步调用成功")
    print(out)

def test_rerank():
    """测试重排序"""
    base_rerank = BaseRerank(**ModelSelector.qwen8b_rerank)
    time_start = time.time()
    instruction = "You are an academic literature evaluation expert. Given a user-provided query topic and a candidate paper, you must strictly assess the semantic relevance between the two."
    result = base_rerank.rerank("量子机器学习算法", ["量子机器学习算法"*20, "量子计算"*10, "量子计算机"*20, "量子力学"*20, "量子物理"*20, "量子信息"*20] * 15, instruction=instruction)
    time_end = time.time()
    print(result)
    print(f"时间: {time_end - time_start} 秒")

def test_tool_call():
    # 测试 ToolLLM
    async def test_tool_call():
        print("\n开始 ToolLLM 测试")
        # 注意：并非所有模型都支持 Tool Calling，这里使用 qwen 作为示例，实际可能需要更换支持的模型
        # 请确保 ModelSelector.qwen25_14b 配置了正确的 API Key 和 Base URL
        try:
            tool_llm = ToolLLM(**ModelSelector.qwen3_30b_a3b) # 替换为支持工具调用的模型

            tools = [
                {
                    "type": "function",
                    "function": {
                        "name": "get_current_weather",
                        "description": "Get the current weather in a given location",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "location": {
                                    "type": "string",
                                    "description": "The city and state, e.g. San Francisco, CA",
                                },
                                "unit": {"type": "string", "enum": ["celsius", "fahrenheit"]},
                            },
                            "required": ["location"],
                        },
                    },
                }
            ]

            prompt = "What's the weather like in Boston?"
            result = await tool_llm.ask_async(prompt, tools=tools)

            print("ToolLLM 调用结果:")
            print(json.dumps(result, indent=2, ensure_ascii=False))

            if result.get("type") == "tool_call":
                print("\n模型请求调用工具:")
                for call in result.get("calls", []):
                    function_name = call.get("function", {}).get("name")
                    arguments = call.get("function", {}).get("arguments")
                    print(f"  函数名: {function_name}")
                    print(f"  参数: {arguments}")
            elif result.get("type") == "text":
                print("\n模型返回文本:")
                print(result.get("content"))
        except Exception as e:
            print(f"ToolLLM 测试失败: {e}")

    asyncio.run(test_tool_call()) # 运行 ToolLLM 测试

def test_tool_call_stream():
    """测试 ToolLLM 流式调用"""
    async def test_tool_call_stream():
        print("\n开始 ToolLLM 流式测试")
        try:
            # 确保使用支持工具调用的模型
            tool_llm = ToolLLM(**ModelSelector.qwen3_30b_a3b)

            tools = [
                {
                    "type": "function",
                    "function": {
                        "name": "get_current_weather",
                        "description": "Get the current weather in a given location",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "location": {
                                    "type": "string",
                                    "description": "The city and state, e.g. San Francisco, CA",
                                },
                                "unit": {"type": "string", "enum": ["celsius", "fahrenheit"]},
                            },
                            "required": ["location"],
                        },
                    },
                }
            ]

            prompt = "What's the weather like in Boston tomorrow in Celsius?"
            print(f"用户: {prompt}")

            final_tool_calls = None
            async for chunk in tool_llm.ask_async_stream_with_tools(prompt, tools=tools):
                if chunk["type"] == "text":
                    print(f"模型文本: {chunk['content']}", end="", flush=True)
                elif chunk["type"] == "think":
                     print(f"\n模型思考: {chunk['content']}", end="", flush=True)
                elif chunk["type"] == "tool_call":
                    print("\n模型请求调用工具 (流结束时收集):")
                    final_tool_calls = chunk["calls"]
                    print(json.dumps(final_tool_calls, indent=2, ensure_ascii=False))
                    # 在这里尝试解析 arguments
                    try:
                        for call in final_tool_calls:
                            if call.get("function") and call["function"].get("arguments"):
                                args_str = call["function"]["arguments"]
                                parsed_args = json.loads(args_str)
                                print(f"  解析后的参数 (调用 {call.get('id', '?')} - {call['function']['name']}): {parsed_args}")
                            else:
                                print(f"  调用 {call.get('id', '?')} 缺少 function 或 arguments")
                    except json.JSONDecodeError as e:
                        print(f"  解析 arguments 失败: {e}")
                        print(f"  原始 arguments 字符串: {args_str}")
                    except Exception as e:
                        print(f"  处理 tool calls 时发生未知错误: {e}")

                else:
                    print(f"\n未知类型块: {chunk}")

            # 可以在这里处理 final_tool_calls
            if final_tool_calls:
                print("\n--- 流处理完成 ---")
                # 在实际应用中，你会在这里调用你的 Python 函数
                # E.g., call_function(final_tool_calls[0]['function']['name'], json.loads(final_tool_calls[0]['function']['arguments']))
            else:
                 print("\n--- 流处理完成 (无工具调用) ---")

        except Exception as e:
            print(f"\nToolLLM 流式测试失败: {e}")

    asyncio.run(test_tool_call_stream()) # 运行流式测试

async def example_usage():
    llm = BaseLLM(**ModelSelector.qwen25_14b)
    async for chunk in llm.ask_async_stream("讲一个故事"):
        print(chunk, end="", flush=True)  # 实时打印每个文本片段

def test_ask_async_stream():
    """测试 BaseLLM 的 ask_async_stream 异步流式输出"""
    async def inner():
        print("\n开始 ask_async_stream 测试")
        try:
            print(ModelSelector.qwen25_14b)
            llm = BaseLLM(**ModelSelector.qwen25_14b)  # 选择一个支持流式的模型
            prompt = "请用中文讲一个简短的故事"
            print(f"用户: {prompt}")
            async for chunk in llm.ask_async_stream(prompt):
                print(f"模型输出: {chunk}", end="", flush=True)
            print("\nask_async_stream 测试完成")
        except Exception as e:
            print(f"ask_async_stream 测试失败: {e}")

    import asyncio
    asyncio.run(inner())

if __name__ == "__main__":
    # 测试同步调用
    # test_sync()

    # 测试异步调用
    asyncio.run(test_async())
    # test_ask_async_stream()

    # ======

    # 测试 Embedding
    # base_embedding = BaseEmbedding(**ModelSelector.sc_qwen3_embedding_4b)
    # result = base_embedding.embedding(["你好", "我是一个测试文本"])
    # for i, embedding in enumerate(result):
    #     print(f"文本 {i + 1} 的 Embedding 向量：{len(embedding)}")

    # ======

    # 测试重排序
    # test_rerank()
    # 测试 ToolLLM 流式调用
    pass